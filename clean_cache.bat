@echo off
echo Cleaning Chatterbox Cache and Temporary Files...
echo.

REM Activate environment
call conda activate chatterbox
if errorlevel 1 (
    echo ERROR: Failed to activate chatterbox environment
    pause
    exit /b 1
)

echo Cleaning Python cache files...
for /r . %%i in (__pycache__) do (
    if exist "%%i" (
        echo Removing: %%i
        rmdir /s /q "%%i"
    )
)

for /r . %%i in (*.pyc) do (
    if exist "%%i" (
        echo Removing: %%i
        del "%%i"
    )
)

for /r . %%i in (*.pyo) do (
    if exist "%%i" (
        echo Removing: %%i
        del "%%i"
    )
)

echo.
echo Cleaning Hugging Face cache (model files)...
echo WARNING: This will remove downloaded model files and they will need to be re-downloaded
echo.
set /p choice="Do you want to clean model cache? (y/N): "
if /i "%choice%"=="y" (
    if exist "%USERPROFILE%\.cache\huggingface" (
        echo Removing Hugging Face cache...
        rmdir /s /q "%USERPROFILE%\.cache\huggingface"
        echo Model cache cleared. Models will be re-downloaded on next run.
    ) else (
        echo No Hugging Face cache found.
    )
) else (
    echo Skipping model cache cleanup.
)

echo.
echo Cleaning pip cache...
pip cache purge

echo.
echo ========================================
echo Cache cleanup completed!
echo ========================================
echo.
echo Note: If you cleaned the model cache, the apps will need to
echo re-download models (~3GB total) on the next run.
echo.
pause
