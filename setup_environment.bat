@echo off
echo Setting up Chatterbox Environment...
echo.

REM Check if conda is available
where conda >nul 2>nul
if errorlevel 1 (
    echo ERROR: Conda is not installed or not in PATH
    echo Please install <PERSON>conda or Anaconda first
    echo Download from: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo Conda found. Creating chatterbox environment...
echo.

REM Create conda environment
call conda create -n chatterbox python=3.11 -y
if errorlevel 1 (
    echo ERROR: Failed to create conda environment
    pause
    exit /b 1
)

echo Environment created successfully. Activating...
echo.

REM Activate environment
call conda activate chatterbox
if errorlevel 1 (
    echo ERROR: Failed to activate chatterbox environment
    pause
    exit /b 1
)

echo Installing Chatterbox package in development mode...
echo.

REM Install package in development mode
pip install -e .
if errorlevel 1 (
    echo ERROR: Failed to install chatterbox package
    pause
    exit /b 1
)

echo Installing Gradio for web interface...
echo.

REM Install gradio
pip install gradio
if errorlevel 1 (
    echo ERROR: Failed to install gradio
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo You can now run the Gradio apps using:
echo - run_gradio_tts_app.bat
echo - run_gradio_vc_app.bat
echo.
echo Or activate the environment manually with:
echo conda activate chatterbox
echo.
pause
