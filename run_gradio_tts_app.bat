@echo off
echo Starting Chatterbox Text-to-Speech Gradio App...
echo.

REM Activate the chatterbox conda environment and run the TTS app
call conda activate chatterbox
if errorlevel 1 (
    echo ERROR: Failed to activate chatterbox environment
    echo Please make sure conda is installed and the chatterbox environment exists
    echo.
    echo To create the environment, run:
    echo conda create -n chatterbox python=3.11
    echo conda activate chatterbox
    echo pip install -e .
    echo pip install gradio
    pause
    exit /b 1
)

echo Chatterbox environment activated successfully
echo.

REM Check if the gradio_tts_app.py file exists
if not exist "gradio_tts_app.py" (
    echo ERROR: gradio_tts_app.py not found in current directory
    echo Please make sure you're running this batch file from the chatterbox project root
    pause
    exit /b 1
)

echo Running Gradio TTS App...
echo The app will be available at: http://127.0.0.1:7861
echo A public URL will also be generated and displayed below
echo Press Ctrl+C to stop the app
echo.

python gradio_tts_app.py

echo.
echo App has stopped.
pause
