# Chatterbox Gradio Apps Guide

This guide explains how to run the Gradio web applications for Chatterbox TTS and Voice Conversion.

## Prerequisites

Make sure you have the chatterbox conda environment set up and activated:

```bash
# Activate the chatterbox environment
conda activate chatterbox

# Verify installation
python -c "import torch; print('Torch version:', torch.__version__)"
python -c "from chatterbox.tts import ChatterboxTTS; print('Chatterbox TTS imported successfully')"
python -c "from chatterbox.vc import ChatterboxVC; print('Chatterbox VC imported successfully')"
```

## 1. Text-to-Speech (TTS) App

The TTS app provides a web interface for generating speech from text with various customization options.

### Running the TTS App

```bash
# Make sure you're in the chatterbox environment
conda activate chatterbox

# Run the TTS app
python gradio_tts_app.py
```

### TTS App Features

- **Text Input:** Enter text to synthesize (up to 300 characters)
- **Reference Audio:** Upload an audio file or record using microphone for voice cloning
- **Exaggeration Control:** Adjust emotional intensity (0.25-2.0, neutral=0.5)
- **CFG/Pace Control:** Control generation pace (0.0-1.0)
- **Advanced Options:**
  - Random seed for reproducible results
  - Temperature control (0.05-5.0)
  - Sampling parameters (min_p, top_p, repetition_penalty)

### TTS App Access

- **Local URL:** http://127.0.0.1:7861
- **Public URL:** Automatically generated and displayed in terminal (expires in 1 week)

### TTS App Usage Tips

- Default settings work well for most use cases
- For expressive speech, try higher exaggeration values (0.7+) with lower CFG weight (~0.3)
- Use reference audio for voice cloning - the model will mimic the voice characteristics
- Experiment with temperature for different speech variations

## 2. Voice Conversion (VC) App

The VC app allows you to convert the voice characteristics of one audio file to match another.

### Running the VC App

```bash
# Make sure you're in the chatterbox environment
conda activate chatterbox

# Run the VC app
python gradio_vc_app.py
```

### VC App Features

- **Input Audio:** Upload audio file or record using microphone
- **Target Voice:** Upload target voice audio (optional - uses default if not provided)
- **Real-time Processing:** Converts voice characteristics in real-time

### VC App Access

- **Local URL:** http://127.0.0.1:7860

### VC App Usage Tips

- Input audio should be clear speech
- Target voice audio should be a good quality sample of the desired voice
- If no target voice is provided, the default voice will be used
- Processing time depends on audio length and system performance

## Running Both Apps Simultaneously

You can run both apps at the same time using separate terminal windows:

### Terminal 1 (TTS App)
```bash
conda activate chatterbox
python gradio_tts_app.py
```

### Terminal 2 (VC App)
```bash
conda activate chatterbox
python gradio_vc_app.py
```

The apps will automatically use different ports:
- **VC App:** http://127.0.0.1:7860
- **TTS App:** http://127.0.0.1:7861

## Model Downloads

On first run, the apps will automatically download required model files:

### TTS Models (~2.1GB total)
- `t3_cfg.safetensors` (2.13GB) - Main TTS model
- `ve.safetensors` (5.70MB) - Voice encoder
- `tokenizer.json` (25.5KB) - Text tokenizer

### VC Models (~1.1GB total)
- `s3gen.safetensors` (1.06GB) - Voice conversion model
- `conds.pt` (107KB) - Conditioning data

Models are cached locally and don't need to be re-downloaded.

## Troubleshooting

### Common Issues

1. **ModuleNotFoundError: No module named 'torch'**
   ```bash
   conda activate chatterbox
   pip install torch==2.6.0 torchaudio==2.6.0
   ```

2. **Import errors for chatterbox modules**
   ```bash
   conda activate chatterbox
   pip install -e .
   ```

3. **Gradio not found**
   ```bash
   conda activate chatterbox
   pip install gradio
   ```

4. **Port already in use**
   - The apps will automatically find available ports if defaults are busy
   - Check terminal output for the actual URLs

5. **CUDA not available warning**
   - This is normal - the apps work fine on CPU
   - Generation will be slower but still functional

### Performance Notes

- **CPU Mode:** Both apps work on CPU but generation is slower
- **Memory Usage:** TTS app uses more memory due to larger models
- **Generation Time:** 
  - TTS: ~1-2 minutes for short text on CPU
  - VC: ~30 seconds to 2 minutes depending on audio length

### System Requirements

- **RAM:** 8GB+ recommended (models are large)
- **Storage:** ~4GB free space for model downloads
- **Python:** 3.8+ (3.11 recommended)
- **OS:** Windows, macOS, or Linux

## Example Workflows

### TTS Workflow
1. Start the TTS app: `python gradio_tts_app.py`
2. Open http://127.0.0.1:7861 in your browser
3. Enter text in the text box
4. (Optional) Upload reference audio for voice cloning
5. Adjust parameters as needed
6. Click "Generate" and wait for audio output
7. Download or play the generated audio

### VC Workflow
1. Start the VC app: `python gradio_vc_app.py`
2. Open http://127.0.0.1:7860 in your browser
3. Upload input audio file or record using microphone
4. (Optional) Upload target voice audio
5. The conversion will process automatically
6. Download or play the converted audio

## Support

If you encounter issues:
1. Check that you're in the correct conda environment
2. Verify all dependencies are installed
3. Check the terminal output for error messages
4. Ensure you have sufficient disk space and memory
5. Try restarting the apps if they become unresponsive

For more information, refer to the main README.md file or visit the project repository.
