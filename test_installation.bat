@echo off
echo Testing Chatterbox Installation...
echo.

REM Check if conda is available
where conda >nul 2>nul
if errorlevel 1 (
    echo [FAIL] Conda is not installed or not in PATH
    echo Please install <PERSON>conda or Anaconda first
    pause
    exit /b 1
) else (
    echo [PASS] Conda is available
)

REM Activate environment
call conda activate chatterbox
if errorlevel 1 (
    echo [FAIL] Failed to activate chatterbox environment
    echo Please run setup_environment.bat first
    pause
    exit /b 1
) else (
    echo [PASS] Chatterbox environment activated
)

echo.
echo Testing Python imports...
echo.

REM Test torch import
python -c "import torch; print('[PASS] PyTorch version:', torch.__version__)" 2>nul
if errorlevel 1 (
    echo [FAIL] PyTorch import failed
    echo Please reinstall with: pip install torch==2.6.0 torchaudio==2.6.0
    pause
    exit /b 1
)

REM Test gradio import
python -c "import gradio; print('[PASS] Gradio version:', gradio.__version__)" 2>nul
if errorlevel 1 (
    echo [FAIL] Gradio import failed
    echo Please install with: pip install gradio
    pause
    exit /b 1
)

REM Test chatterbox TTS import
python -c "from chatterbox.tts import ChatterboxTTS; print('[PASS] Chatterbox TTS imported successfully')" 2>nul
if errorlevel 1 (
    echo [FAIL] Chatterbox TTS import failed
    echo Please reinstall with: pip install -e .
    pause
    exit /b 1
)

REM Test chatterbox VC import
python -c "from chatterbox.vc import ChatterboxVC; print('[PASS] Chatterbox VC imported successfully')" 2>nul
if errorlevel 1 (
    echo [FAIL] Chatterbox VC import failed
    echo Please reinstall with: pip install -e .
    pause
    exit /b 1
)

echo.
echo Testing CUDA availability...
python -c "import torch; print('[INFO] CUDA available:', torch.cuda.is_available())"

echo.
echo Checking required files...
echo.

if exist "gradio_tts_app.py" (
    echo [PASS] gradio_tts_app.py found
) else (
    echo [FAIL] gradio_tts_app.py not found
)

if exist "gradio_vc_app.py" (
    echo [PASS] gradio_vc_app.py found
) else (
    echo [FAIL] gradio_vc_app.py not found
)

if exist "run_gradio_tts_app.bat" (
    echo [PASS] run_gradio_tts_app.bat found
) else (
    echo [FAIL] run_gradio_tts_app.bat not found
)

if exist "run_gradio_vc_app.bat" (
    echo [PASS] run_gradio_vc_app.bat found
) else (
    echo [FAIL] run_gradio_vc_app.bat not found
)

echo.
echo ========================================
echo Installation test completed!
echo ========================================
echo.
echo If all tests passed, you can run:
echo - run_gradio_tts_app.bat (for TTS)
echo - run_gradio_vc_app.bat (for VC)
echo - run_both_apps.bat (for both)
echo.
pause
