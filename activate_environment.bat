@echo off
echo Activating Chatterbox Environment...
echo.

REM Check if conda is available
where conda >nul 2>nul
if errorlevel 1 (
    echo ERROR: Conda is not installed or not in PATH
    echo Please install <PERSON><PERSON>da or Anaconda first
    pause
    exit /b 1
)

REM Activate the chatterbox environment and keep the command prompt open
call conda activate chatterbox
if errorlevel 1 (
    echo ERROR: Failed to activate chatterbox environment
    echo Please run setup_environment.bat first to create the environment
    pause
    exit /b 1
)

echo Chatterbox environment activated successfully!
echo.
echo You can now run Python commands with access to:
echo - PyTorch
echo - Chatterbox TTS and VC
echo - Gradio
echo.
echo Available commands:
echo   python gradio_tts_app.py    - Run TTS app
echo   python gradio_vc_app.py     - Run VC app
echo   python -c "import chatterbox" - Test import
echo.
echo Type 'exit' to close this window.
echo.

REM Keep the command prompt open with the activated environment
cmd /k
